import { SysStorage } from './storage';
export class IndexedDBStorage<T> implements SysStorage<T> {
  private dbName: string;
  private storeName: string;
  private version: number;
  private db: IDBDatabase | null = null;

  constructor(
    dbName: string = 'DefaultDB',
    storeName: string = 'keyValueStore',
    version: number = 1,
  ) {
    this.dbName = dbName;
    this.storeName = storeName;
    this.version = version;
  }

  /**
   * 初始化 IndexedDB 数据库
   * @returns Promise<IDBDatabase>
   */
  private async initDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    // 检查 IndexedDB 是否可用
    if (typeof window === 'undefined' || !window.indexedDB) {
      throw new Error('IndexedDB is not available');
    }

    return new Promise((resolve, reject) => {
      const request = window.indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error(`Failed to open IndexedDB: ${request.error?.message}`));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建对象存储（如果不存在）
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName, { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * 从 IndexedDB 获取指定 key 的值
   * @param key 存储的键名
   * @returns Promise<T | null> 返回存储的值或 null
   */
  async getItem(key: string): Promise<T | null> {
    try {
      const db = await this.initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.get(key);

        request.onerror = () => {
          reject(new Error(`Failed to get item: ${request.error?.message}`));
        };

        request.onsuccess = () => {
          const result = request.result;
          resolve(result ? result.value : null);
        };
      });
    } catch (error) {
      console.error('Error getting item from IndexedDB:', error);
      return null;
    }
  }

  /**
   * 向 IndexedDB 存储指定 key 的值
   * @param key 存储的键名
   * @param value 要存储的值
   * @returns Promise<void>
   */
  async setItem(key: string, value: T): Promise<void> {
    try {
      const db = await this.initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.put({ key, value });

        request.onerror = () => {
          reject(new Error(`Failed to set item: ${request.error?.message}`));
        };

        request.onsuccess = () => {
          resolve();
        };
      });
    } catch (error) {
      console.error('Error setting item to IndexedDB:', error);
      throw error;
    }
  }

  /**
   * 从 IndexedDB 删除指定 key 的值
   * @param key 要删除的键名
   * @returns Promise<void>
   */
  async removeItem(key: string): Promise<void> {
    try {
      const db = await this.initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.delete(key);

        request.onerror = () => {
          reject(new Error(`Failed to remove item: ${request.error?.message}`));
        };

        request.onsuccess = () => {
          resolve();
        };
      });
    } catch (error) {
      console.error('Error removing item from IndexedDB:', error);
      throw error;
    }
  }

  /**
   * 清空 IndexedDB 中的所有数据
   * @returns Promise<void>
   */
  async clear(): Promise<void> {
    try {
      const db = await this.initDB();

      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.clear();

        request.onerror = () => {
          reject(new Error(`Failed to clear store: ${request.error?.message}`));
        };

        request.onsuccess = () => {
          resolve();
        };
      });
    } catch (error) {
      console.error('Error clearing IndexedDB:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
