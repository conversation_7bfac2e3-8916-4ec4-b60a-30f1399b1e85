import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SessionStorage } from './storage';

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock window object
Object.defineProperty(globalThis, 'window', {
  value: {
    sessionStorage: sessionStorageMock,
  },
  writable: true,
});

describe('SessionStorage', () => {
  let storage: SessionStorage<any>;

  beforeEach(() => {
    storage = new SessionStorage();
    vi.clearAllMocks();
  });

  describe('getItem', () => {
    it('should return null when item does not exist', async () => {
      sessionStorageMock.getItem.mockReturnValue(null);
      
      const result = await storage.getItem('nonexistent');
      
      expect(result).toBeNull();
      expect(sessionStorageMock.getItem).toHaveBeenCalledWith('nonexistent');
    });

    it('should return parsed JSON object', async () => {
      const testObject = { name: 'test', value: 123 };
      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(testObject));
      
      const result = await storage.getItem('test-key');
      
      expect(result).toEqual(testObject);
      expect(sessionStorageMock.getItem).toHaveBeenCalledWith('test-key');
    });

    it('should return string value when JSON parsing fails', async () => {
      const testString = 'simple string';
      sessionStorageMock.getItem.mockReturnValue(testString);
      
      const result = await storage.getItem('string-key');
      
      expect(result).toBe(testString);
    });

    it('should return null when sessionStorage throws error', async () => {
      sessionStorageMock.getItem.mockImplementation(() => {
        throw new Error('sessionStorage error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const result = await storage.getItem('error-key');
      
      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('setItem', () => {
    it('should store string value directly', () => {
      const testString = 'test string';
      
      storage.setItem('string-key', testString);
      
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('string-key', testString);
    });

    it('should store object as JSON string', () => {
      const testObject = { name: 'test', value: 123 };
      
      storage.setItem('object-key', testObject);
      
      expect(sessionStorageMock.setItem).toHaveBeenCalledWith('object-key', JSON.stringify(testObject));
    });

    it('should handle sessionStorage errors gracefully', () => {
      sessionStorageMock.setItem.mockImplementation(() => {
        throw new Error('sessionStorage error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => storage.setItem('error-key', 'value')).not.toThrow();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('removeItem', () => {
    it('should remove item from sessionStorage', () => {
      storage.removeItem('test-key');
      
      expect(sessionStorageMock.removeItem).toHaveBeenCalledWith('test-key');
    });

    it('should handle sessionStorage errors gracefully', () => {
      sessionStorageMock.removeItem.mockImplementation(() => {
        throw new Error('sessionStorage error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => storage.removeItem('error-key')).not.toThrow();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('clear', () => {
    it('should clear all items from sessionStorage', () => {
      storage.clear();
      
      expect(sessionStorageMock.clear).toHaveBeenCalled();
    });

    it('should handle sessionStorage errors gracefully', () => {
      sessionStorageMock.clear.mockImplementation(() => {
        throw new Error('sessionStorage error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => storage.clear()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('when sessionStorage is not available', () => {
    beforeEach(() => {
      // Mock window.sessionStorage as undefined
      Object.defineProperty(globalThis, 'window', {
        value: {
          sessionStorage: undefined,
        },
        writable: true,
      });
      storage = new SessionStorage();
    });

    it('should return null for getItem', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const result = await storage.getItem('test');
      
      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('sessionStorage is not available');
      
      consoleSpy.mockRestore();
    });

    it('should do nothing for setItem', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      expect(() => storage.setItem('test', 'value')).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith('sessionStorage is not available');
      
      consoleSpy.mockRestore();
    });

    it('should do nothing for removeItem', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      expect(() => storage.removeItem('test')).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith('sessionStorage is not available');
      
      consoleSpy.mockRestore();
    });

    it('should do nothing for clear', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      expect(() => storage.clear()).not.toThrow();
      expect(consoleSpy).toHaveBeenCalledWith('sessionStorage is not available');
      
      consoleSpy.mockRestore();
    });
  });

  describe('type safety', () => {
    beforeEach(() => {
      // Reset window mock to have sessionStorage available for type safety tests
      Object.defineProperty(globalThis, 'window', {
        value: {
          sessionStorage: sessionStorageMock,
        },
        writable: true,
      });
      storage = new SessionStorage();
      vi.clearAllMocks();
    });

    it('should work with typed storage', async () => {
      interface User {
        id: number;
        name: string;
        email: string;
      }

      const userStorage = new SessionStorage<User>();
      const testUser: User = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      };

      sessionStorageMock.getItem.mockReturnValue(JSON.stringify(testUser));

      const result = await userStorage.getItem('user');

      expect(result).toEqual(testUser);
      expect(typeof result?.id).toBe('number');
      expect(typeof result?.name).toBe('string');
    });

    it('should work with string storage', async () => {
      const stringStorage = new SessionStorage<string>();
      const testString = 'hello world';

      sessionStorageMock.getItem.mockReturnValue(testString);

      const result = await stringStorage.getItem('message');

      expect(result).toBe(testString);
      expect(typeof result).toBe('string');
    });
  });
});
