import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { IndexedDBStorage } from './storage';

describe('IndexedDBStorage', () => {
  let storage: IndexedDBStorage<any>;

  beforeEach(() => {
    // Create a simple mock for IndexedDB
    const mockDB = {
      transaction: vi.fn().mockReturnValue({
        objectStore: vi.fn().mockReturnValue({
          get: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null,
            result: null
          }),
          put: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          }),
          delete: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          }),
          clear: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          })
        })
      }),
      close: vi.fn(),
      objectStoreNames: {
        contains: vi.fn().mockReturnValue(true)
      },
      createObjectStore: vi.fn()
    };

    const mockIndexedDB = {
      open: vi.fn().mockReturnValue({
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockDB
      })
    };

    // Mock window.indexedDB
    Object.defineProperty(globalThis, 'window', {
      value: {
        indexedDB: mockIndexedDB,
      },
      writable: true,
    });

    storage = new IndexedDBStorage('testDB', 'testStore', 1);
  });

  afterEach(() => {
    storage.close();
  });

  describe('constructor', () => {
    it('should create instance with default parameters', () => {
      const defaultStorage = new IndexedDBStorage();
      expect(defaultStorage).toBeInstanceOf(IndexedDBStorage);
    });

    it('should create instance with custom parameters', () => {
      const customStorage = new IndexedDBStorage('customDB', 'customStore', 2);
      expect(customStorage).toBeInstanceOf(IndexedDBStorage);
    });
  });

  describe('error handling', () => {
    it('should handle IndexedDB not available', async () => {
      Object.defineProperty(globalThis, 'window', {
        value: {
          indexedDB: undefined,
        },
        writable: true,
      });

      const errorStorage = new IndexedDBStorage();

      // getItem should return null
      const getResult = await errorStorage.getItem('test');
      expect(getResult).toBeNull();

      // setItem should throw error
      await expect(errorStorage.setItem('test', 'value')).rejects.toThrow('IndexedDB is not available');

      // removeItem should throw error
      await expect(errorStorage.removeItem('test')).rejects.toThrow('IndexedDB is not available');

      // clear should throw error
      await expect(errorStorage.clear()).rejects.toThrow('IndexedDB is not available');
    });
  });

  describe('close', () => {
    it('should close database connection', () => {
      expect(() => storage.close()).not.toThrow();
    });
  });

  describe('basic functionality', () => {
    it('should be instantiable', () => {
      expect(storage).toBeInstanceOf(IndexedDBStorage);
    });
  });
});
