import { User } from '@moxo/proto';
import { uuid } from '@moxo/shared';
type SubscriptionId = string;
interface SubscribeCallback {
  (): void;
}
interface CacheUpdateFunction<T> {
  (arg: T): T;
}
export enum CacheType {
  sessionStorage = 'sessionStorage',
  localStorage = 'localStorage',
  indexDB = 'indexDB',
}
interface CacheOption {
  cacheType: CacheType;
  cacheKey: string;
}
export interface ICache<T> {
  constructor(option: CacheOption): unknown;
  subscribe(fn: SubscribeCallback): SubscriptionId;
  unsubscribe(id: SubscriptionId): void;
  update(fn: CacheUpdateFunction<T>): void;
}
const SysCacheSymbol: unique symbol = Symbol('cacheData');
export class SysCache<T> implements ICache<T> {
  private subscriber: Map<SubscriptionId, SubscribeCallback>;
  private [SysCacheSymbol]: T;
  private option: CacheOption;
  constructor(option: CacheOption) {
    this.subscriber = new Map();
    this[SysCacheSymbol] = {} as T;
    this.option = option || ({} as CacheOption);
  }
  subscribe(fn: SubscribeCallback): SubscriptionId {
    const id = uuid();
    this.subscriber.set(id, fn);
    return id;
  }
  unsubscribe(id: SubscriptionId) {
    this.subscriber.delete(id);
  }
  update(fn: CacheUpdateFunction<T>) {
    const data = this[SysCacheSymbol];
    this[SysCacheSymbol] = fn(data);
    this.subscriber.forEach((fn) => fn());
  }
}
